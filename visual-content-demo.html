<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Visual Content Strategy Demo | CalculatorSuites</title>
  <link rel="stylesheet" href="assets/css/main.css">
  <link rel="stylesheet" href="assets/css/calculator.css">
  <link rel="stylesheet" href="assets/css/responsive.css">
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap"
    rel="stylesheet">
</head>

<body>
  <header class="site-header">
    <div class="container">
      <div class="nav-container">
        <a href="./" class="logo">
          <span class="logo-text">Calculator Suites</span>
        </a>
      </div>
    </div>
  </header>

  <main class="main-content">
    <div class="container">
      <h1>Visual Content Strategy Implementation Demo</h1>
      <p class="lead">Showcasing all visual content components implemented for CalculatorSuites financial calculators.
      </p>

      <!-- SIP vs Lump Sum Comparison -->
      <section class="demo-section">
        <h2>1. Comparison Infographics</h2>
        <div id="demo-comparison"></div>
      </section>

      <!-- Micro-Infographic -->
      <section class="demo-section">
        <h2>2. Micro-Infographics for Social Media</h2>
        <div id="demo-micro-infographic"></div>
      </section>

      <!-- Financial Journey -->
      <section class="demo-section">
        <h2>3. Data Storytelling: Financial Journey</h2>
        <div id="demo-financial-journey"></div>
      </section>

      <!-- Interactive Timeline -->
      <section class="demo-section">
        <h2>4. Interactive Timeline Visualization</h2>
        <div id="demo-timeline"></div>
      </section>

      <!-- Interactive Slider -->
      <section class="demo-section">
        <h2>5. Interactive Tax Regime Comparison Slider</h2>
        <div id="demo-slider"></div>
      </section>

      <!-- Process Infographic -->
      <section class="demo-section">
        <h2>6. Step-by-Step Process Infographics</h2>
        <div id="demo-process"></div>
      </section>

      <!-- Enhanced Chart -->
      <section class="demo-section">
        <h2>7. Enhanced Charts with Interactive Controls</h2>
        <div id="demo-chart"></div>
      </section>

      <!-- Quick Tip Graphics -->
      <section class="demo-section">
        <h2>8. Quick Tip Graphics</h2>
        <div class="quick-tip-graphic">
          <div class="quick-tip-title">Pro Tax Tip</div>
          <div class="quick-tip-content">Invest in ELSS funds to save tax under Section 80C while building wealth for
            the long term.</div>
          <div class="quick-tip-highlight">Save up to ₹46,800 in taxes annually with ₹1.5L investment!</div>
        </div>
      </section>

      <!-- Share Optimized Graphics -->
      <section class="demo-section">
        <h2>9. Social Share Optimized Graphics</h2>
        <div class="share-optimized-graphic">
          <div class="share-graphic-logo">CalculatorSuites</div>
          <div class="share-graphic-title">SIP Investment Reality</div>
          <div class="share-graphic-stat">
            <span class="share-graphic-stat-value">₹2.3Cr</span>
            <span class="share-graphic-stat-label">Wealth from ₹10K monthly SIP in 20 years</span>
          </div>
          <div class="share-graphic-footer">Start your SIP journey today!</div>
        </div>
      </section>
    </div>
  </main>

  <script src="assets/js/utils.js"></script>
  <script src="assets/js/visual-components.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Demo 1: Comparison Infographic
      VisualComponents.createComparisonInfographic({
        containerId: 'demo-comparison',
        title: 'SIP vs Lump Sum Investment Comparison',
        leftSide: {
          theme: 'sip',
          icon: '📈',
          title: 'SIP Investment',
          metrics: [
            { label: 'Monthly Investment', value: '₹10,000' },
            { label: 'Total Investment', value: '₹24,00,000' },
            { label: 'Final Value', value: '₹2,28,00,000' },
            { label: 'Returns', value: '₹2,04,00,000' }
          ]
        },
        rightSide: {
          theme: 'lump-sum',
          icon: '💰',
          title: 'Lump Sum Investment',
          metrics: [
            { label: 'Initial Investment', value: '₹24,00,000' },
            { label: 'Total Investment', value: '₹24,00,000' },
            { label: 'Final Value', value: '₹2,40,00,000' },
            { label: 'Returns', value: '₹2,16,00,000' }
          ]
        },
        insight: 'SIP provides rupee-cost averaging benefits and reduces timing risk, while lump sum can generate higher returns with immediate market exposure.'
      });

      // Demo 2: Micro-Infographic
      VisualComponents.createMicroInfographic({
        containerId: 'demo-micro-infographic',
        theme: 'investment',
        hook: 'SIP Magic in Action!',
        content: 'Investing just ₹5,000 monthly for 15 years',
        stats: [
          { value: '₹1.8Cr', label: 'Total Wealth Created' },
          { value: '240%', label: 'Total Returns Generated' }
        ]
      });

      // Demo 3: Financial Journey
      VisualComponents.createFinancialJourney({
        containerId: 'demo-financial-journey',
        title: 'Your Home Loan EMI Journey',
        character: 'homebuyer',
        phases: [
          {
            type: 'heavy-interest',
            icon: '🏠',
            title: 'Years 1-7: Heavy Interest Phase',
            description: 'In the early years, ₹45,000 EMI is mostly interest. About 70% goes to interest, only 30% reduces your principal.',
            insight: 'Priya pays ₹31,500 in interest, only ₹13,500 towards principal'
          },
          {
            type: 'turning-point',
            icon: '⚖️',
            title: 'Year 10: The Turning Point',
            description: 'Around the midpoint of your loan, interest and principal payments become equal.',
            insight: 'Interest and principal payments now equal - the balance tips in your favor'
          },
          {
            type: 'wealth-building',
            icon: '🚀',
            title: 'Years 15-20: Principal Dominance',
            description: 'In the final phase, 70% of your EMI reduces the principal amount.',
            insight: '70% of payments now reduce the actual loan amount - wealth building phase!'
          }
        ]
      });

      // Demo 4: Timeline Visualization
      VisualComponents.createTimelineVisualization({
        containerId: 'demo-timeline',
        title: 'Your SIP Growth Timeline',
        points: [
          { year: 0, label: 'Year 0', value: '₹0' },
          { year: 5, label: 'Year 5', value: '₹8.5L' },
          { year: 10, label: 'Year 10', value: '₹23L' },
          { year: 15, label: 'Year 15', value: '₹52L' },
          { year: 20, label: 'Year 20', value: '₹1.2Cr' }
        ]
      });

      // Demo 5: Interactive Slider
      VisualComponents.createInteractiveSlider({
        containerId: 'demo-slider',
        title: 'Tax Regime Comparison: Find Your Optimal Choice',
        sliders: [
          {
            label: 'Annual Salary (₹)',
            min: 300000,
            max: 2000000,
            step: 50000,
            value: 800000,
            format: 'currency'
          },
          {
            label: '80C Investments (₹)',
            min: 0,
            max: 150000,
            step: 10000,
            value: 100000,
            format: 'currency'
          }
        ],
        onUpdate: function (values, resultsContainerId) {
          const [salary, investments] = values;
          const oldRegimeTax = Math.max(0, (salary - 50000 - investments - 250000) * 0.2);
          const newRegimeTax = Math.max(0, (salary - 300000) * 0.15);
          const betterRegime = oldRegimeTax <= newRegimeTax ? 'Old Regime' : 'New Regime';
          const savings = Math.abs(oldRegimeTax - newRegimeTax);

          const resultsContainer = document.getElementById(resultsContainerId);
          if (resultsContainer) {
            resultsContainer.innerHTML = `
              <div class="slider-result-item">
                <span class="slider-result-label">Old Regime Tax</span>
                <span class="slider-result-value">₹${oldRegimeTax.toLocaleString()}</span>
              </div>
              <div class="slider-result-item">
                <span class="slider-result-label">New Regime Tax</span>
                <span class="slider-result-value">₹${newRegimeTax.toLocaleString()}</span>
              </div>
              <div class="slider-result-item slider-result-highlight">
                <span class="slider-result-label">Recommended: ${betterRegime}</span>
                <span class="slider-result-value">Save ₹${savings.toLocaleString()}</span>
              </div>
            `;
          }
        }
      });

      // Demo 6: Process Infographic
      const processHtml = `
        <div class="process-infographic">
          <h3 class="process-title">4 Steps to Calculate Your Perfect EMI</h3>
          <div class="process-steps">
            <div class="process-step fade-in-up">
              <div class="process-step-number">1</div>
              <div class="process-step-icon">💰</div>
              <h4 class="process-step-title">Enter Loan Amount</h4>
              <p class="process-step-description">Input the total loan amount you need. Consider your down payment and property value.</p>
              <button class="process-step-action">Enter Amount</button>
            </div>
            <div class="process-step fade-in-up">
              <div class="process-step-number">2</div>
              <div class="process-step-icon">📊</div>
              <h4 class="process-step-title">Set Interest Rate</h4>
              <p class="process-step-description">Enter the annual interest rate offered by your lender.</p>
              <button class="process-step-action">Set Rate</button>
            </div>
            <div class="process-step fade-in-up">
              <div class="process-step-number">3</div>
              <div class="process-step-icon">⏰</div>
              <h4 class="process-step-title">Choose Tenure</h4>
              <p class="process-step-description">Select loan tenure. Longer tenure = lower EMI but higher total interest.</p>
              <button class="process-step-action">Set Tenure</button>
            </div>
            <div class="process-step fade-in-up">
              <div class="process-step-number">4</div>
              <div class="process-step-icon">🧮</div>
              <h4 class="process-step-title">Calculate & Plan</h4>
              <p class="process-step-description">Get your monthly EMI, total interest, and payment breakdown.</p>
              <button class="process-step-action">Calculate EMI</button>
            </div>
          </div>
        </div>
      `;
      document.getElementById('demo-process').innerHTML = processHtml;

      // Demo 7: Enhanced Chart
      VisualComponents.createEnhancedChart({
        containerId: 'demo-chart',
        title: 'EMI Impact of Different Interest Rates',
        chartType: 'bar',
        data: {
          labels: ['8%', '9%', '10%', '11%'],
          values: [41000, 43500, 46000, 48500],
          colors: ['#38b000', '#4361ee', '#ff9e00', '#f72585'],
          datasets: [{ label: 'Monthly EMI', colorClass: 'loan' }]
        },
        controls: [
          { label: '20 Years', value: '20' },
          { label: '25 Years', value: '25' },
          { label: '30 Years', value: '30' }
        ],
        onControlChange: function (value, containerId) {
          console.log('Chart control changed:', value);
        }
      });
    });
  </script>
</body>

</html>