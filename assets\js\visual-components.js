/**
 * Visual Components Library for CalculatorSuites
 * Implements visual content strategy components including infographics, charts, and interactive elements
 */

// Visual Components Manager
const VisualComponents = {
  
  /**
   * Create a comparison infographic between two financial scenarios
   * @param {Object} config - Configuration object
   */
  createComparisonInfographic: function(config) {
    const {
      containerId,
      title,
      leftSide,
      rightSide,
      insight
    } = config;

    const container = document.getElementById(containerId);
    if (!container) return;

    const html = `
      <div class="comparison-chart-container">
        <h3 class="comparison-chart-title">${title}</h3>
        <div class="comparison-split-view">
          <div class="comparison-side ${leftSide.theme}">
            <h4 class="comparison-side-title">
              <span>${leftSide.icon}</span>
              ${leftSide.title}
            </h4>
            <ul class="comparison-metrics">
              ${leftSide.metrics.map(metric => `
                <li>
                  <span class="metric-label">${metric.label}</span>
                  <span class="metric-value">${metric.value}</span>
                </li>
              `).join('')}
            </ul>
          </div>
          <div class="comparison-side ${rightSide.theme}">
            <h4 class="comparison-side-title">
              <span>${rightSide.icon}</span>
              ${rightSide.title}
            </h4>
            <ul class="comparison-metrics">
              ${rightSide.metrics.map(metric => `
                <li>
                  <span class="metric-label">${metric.label}</span>
                  <span class="metric-value">${metric.value}</span>
                </li>
              `).join('')}
            </ul>
          </div>
        </div>
        ${insight ? `
          <div class="comparison-insight">
            <div class="comparison-insight-title">
              <span>💡</span>
              Key Insight
            </div>
            <div class="comparison-insight-text">${insight}</div>
          </div>
        ` : ''}
      </div>
    `;

    container.innerHTML = html;
  },

  /**
   * Create a micro-infographic for social media sharing
   * @param {Object} config - Configuration object
   */
  createMicroInfographic: function(config) {
    const {
      containerId,
      theme = 'default',
      hook,
      content,
      stats
    } = config;

    const container = document.getElementById(containerId);
    if (!container) return;

    const html = `
      <div class="micro-infographic ${theme}-theme">
        <div class="micro-infographic-hook">${hook}</div>
        <div class="micro-infographic-content">${content}</div>
        ${stats.map(stat => `
          <div class="micro-infographic-stat">
            <span class="micro-infographic-stat-value">${stat.value}</span>
            <span class="micro-infographic-stat-label">${stat.label}</span>
          </div>
        `).join('')}
      </div>
    `;

    container.innerHTML = html;
  },

  /**
   * Create a financial journey storytelling component
   * @param {Object} config - Configuration object
   */
  createFinancialJourney: function(config) {
    const {
      containerId,
      title,
      character,
      phases
    } = config;

    const container = document.getElementById(containerId);
    if (!container) return;

    const html = `
      <div class="financial-journey">
        <h3 class="journey-title">${title}</h3>
        <div class="journey-phases">
          ${phases.map(phase => `
            <div class="journey-phase ${phase.type}">
              <div class="journey-phase-icon">${phase.icon}</div>
              <div class="journey-phase-content">
                <h4 class="journey-phase-title">${phase.title}</h4>
                <p class="journey-phase-description">${phase.description}</p>
                <div class="journey-phase-insight">${phase.insight}</div>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;

    container.innerHTML = html;
  },

  /**
   * Create an interactive timeline visualization
   * @param {Object} config - Configuration object
   */
  createTimelineVisualization: function(config) {
    const {
      containerId,
      title,
      points
    } = config;

    const container = document.getElementById(containerId);
    if (!container) return;

    const html = `
      <div class="timeline-visualization">
        <h3 class="timeline-title">${title}</h3>
        <div class="timeline-container">
          <div class="timeline-line"></div>
          <div class="timeline-points">
            ${points.map(point => `
              <div class="timeline-point" data-year="${point.year}">
                <div class="timeline-point-label">${point.label}</div>
                <div class="timeline-point-value">${point.value}</div>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    `;

    container.innerHTML = html;
  },

  /**
   * Create an interactive slider comparison tool
   * @param {Object} config - Configuration object
   */
  createInteractiveSlider: function(config) {
    const {
      containerId,
      title,
      sliders,
      onUpdate
    } = config;

    const container = document.getElementById(containerId);
    if (!container) return;

    const sliderId = `slider-${Date.now()}`;

    const html = `
      <div class="interactive-slider-container" id="${sliderId}">
        <h3 class="slider-title">${title}</h3>
        <div class="slider-controls">
          ${sliders.map((slider, index) => `
            <div class="slider-control">
              <div class="slider-label">
                <span>${slider.label}</span>
                <span class="slider-value" id="${sliderId}-value-${index}">${slider.value}</span>
              </div>
              <input 
                type="range" 
                class="custom-slider" 
                id="${sliderId}-slider-${index}"
                min="${slider.min}" 
                max="${slider.max}" 
                step="${slider.step || 1}"
                value="${slider.value}"
                data-format="${slider.format || 'number'}"
              >
            </div>
          `).join('')}
        </div>
        <div class="slider-results" id="${sliderId}-results">
          <!-- Results will be populated by onUpdate callback -->
        </div>
      </div>
    `;

    container.innerHTML = html;

    // Add event listeners to sliders
    sliders.forEach((slider, index) => {
      const sliderElement = document.getElementById(`${sliderId}-slider-${index}`);
      const valueElement = document.getElementById(`${sliderId}-value-${index}`);
      
      sliderElement.addEventListener('input', function() {
        const value = parseFloat(this.value);
        const format = this.dataset.format;
        
        // Update display value
        if (format === 'currency') {
          valueElement.textContent = calculatorUtils.formatCurrency(value);
        } else if (format === 'percentage') {
          valueElement.textContent = `${value}%`;
        } else {
          valueElement.textContent = value.toLocaleString();
        }

        // Call update callback with all current values
        if (onUpdate) {
          const allValues = sliders.map((_, i) => {
            const el = document.getElementById(`${sliderId}-slider-${i}`);
            return parseFloat(el.value);
          });
          onUpdate(allValues, `${sliderId}-results`);
        }
      });
    });

    // Trigger initial update
    if (onUpdate) {
      const initialValues = sliders.map(slider => slider.value);
      onUpdate(initialValues, `${sliderId}-results`);
    }
  },

  /**
   * Create an enhanced chart with interactive controls
   * @param {Object} config - Configuration object
   */
  createEnhancedChart: function(config) {
    const {
      containerId,
      title,
      chartType = 'bar',
      data,
      controls = [],
      onControlChange
    } = config;

    const container = document.getElementById(containerId);
    if (!container) return;

    const chartId = `chart-${Date.now()}`;

    const html = `
      <div class="enhanced-chart-container">
        <div class="chart-header">
          <h3 class="chart-title">${title}</h3>
          ${controls.length > 0 ? `
            <div class="chart-controls">
              ${controls.map((control, index) => `
                <button 
                  class="chart-control-btn ${index === 0 ? 'active' : ''}" 
                  data-control="${control.value}"
                  data-index="${index}"
                >
                  ${control.label}
                </button>
              `).join('')}
            </div>
          ` : ''}
        </div>
        <div class="chart-content" id="${chartId}-content">
          <!-- Chart content will be rendered here -->
        </div>
        <div class="chart-legend">
          ${data.datasets ? data.datasets.map(dataset => `
            <div class="legend-item">
              <div class="legend-color ${dataset.colorClass}"></div>
              <span>${dataset.label}</span>
            </div>
          `).join('') : ''}
        </div>
      </div>
    `;

    container.innerHTML = html;

    // Add control event listeners
    if (controls.length > 0) {
      const controlButtons = container.querySelectorAll('.chart-control-btn');
      controlButtons.forEach(button => {
        button.addEventListener('click', function() {
          // Update active state
          controlButtons.forEach(btn => btn.classList.remove('active'));
          this.classList.add('active');
          
          // Call change callback
          if (onControlChange) {
            onControlChange(this.dataset.control, `${chartId}-content`);
          }
        });
      });
    }

    // Render initial chart
    this.renderChart(`${chartId}-content`, chartType, data);
  },

  /**
   * Render a simple chart using HTML/CSS
   * @param {string} containerId - Container element ID
   * @param {string} type - Chart type (bar, pie, line)
   * @param {Object} data - Chart data
   */
  renderChart: function(containerId, type, data) {
    const container = document.getElementById(containerId);
    if (!container) return;

    if (type === 'bar') {
      this.renderBarChart(container, data);
    } else if (type === 'pie') {
      this.renderPieChart(container, data);
    } else if (type === 'line') {
      this.renderLineChart(container, data);
    }
  },

  /**
   * Render a bar chart using HTML/CSS
   */
  renderBarChart: function(container, data) {
    const maxValue = Math.max(...data.values);
    
    const html = `
      <div class="simple-bar-chart">
        ${data.labels.map((label, index) => {
          const value = data.values[index];
          const height = (value / maxValue) * 200; // Max height 200px
          return `
            <div class="bar-item">
              <div class="bar" style="height: ${height}px; background: ${data.colors[index] || 'var(--primary-color)'}">
                <span class="bar-value">${calculatorUtils.formatCurrency(value)}</span>
              </div>
              <span class="bar-label">${label}</span>
            </div>
          `;
        }).join('')}
      </div>
    `;

    container.innerHTML = html;
  },

  /**
   * Render a pie chart using CSS
   */
  renderPieChart: function(container, data) {
    const total = data.values.reduce((sum, value) => sum + value, 0);
    let currentAngle = 0;

    const html = `
      <div class="simple-pie-chart">
        <div class="pie-container">
          ${data.values.map((value, index) => {
            const percentage = (value / total) * 100;
            const angle = (value / total) * 360;
            const slice = `
              <div class="pie-slice" style="
                --start-angle: ${currentAngle}deg;
                --end-angle: ${currentAngle + angle}deg;
                --color: ${data.colors[index] || 'var(--primary-color)'};
              "></div>
            `;
            currentAngle += angle;
            return slice;
          }).join('')}
        </div>
        <div class="pie-legend">
          ${data.labels.map((label, index) => `
            <div class="pie-legend-item">
              <div class="pie-legend-color" style="background: ${data.colors[index]}"></div>
              <span>${label}: ${calculatorUtils.formatCurrency(data.values[index])}</span>
            </div>
          `).join('')}
        </div>
      </div>
    `;

    container.innerHTML = html;
  }
};

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = VisualComponents;
} else {
  window.VisualComponents = VisualComponents;
}
